package com.pony.spring.hello.hello.controller

import com.pony.spring.hello.hello.model.HelloRequest
import com.pony.spring.hello.hello.model.HelloResponse
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api")
@CrossOrigin(origins = ["*"]) // 允许跨域请求，方便移动端调用
class HelloController {

    @PostMapping("/hello")
    fun sayHello(@RequestBody request: HelloRequest): ResponseEntity<HelloResponse> {
        val greeting = "Hello, ${request.name}!"
        val response = HelloResponse(
            greeting = greeting,
            name = request.name,
            message = request.message
        )
        return ResponseEntity.ok(response)
    }

    @GetMapping("/hello")
    fun getHello(): ResponseEntity<Map<String, String>> {
        return ResponseEntity.ok(mapOf(
            "message" to "Hello API is running!",
            "endpoint" to "POST /api/hello"
        ))
    }
}
