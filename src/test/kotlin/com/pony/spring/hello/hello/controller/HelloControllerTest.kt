package com.pony.spring.hello.hello.controller

import com.fasterxml.jackson.databind.ObjectMapper
import com.pony.spring.hello.hello.model.HelloRequest
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.*

@WebMvcTest(HelloController::class)
class HelloControllerTest {

    @Autowired
    private lateinit var mockMvc: MockMvc

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @Test
    fun `should return hello response when POST request is made`() {
        val request = HelloRequest(name = "World", message = "Test message")
        val requestJson = objectMapper.writeValueAsString(request)

        mockMvc.perform(
            post("/api/hello")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson)
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.greeting").value("Hello, World!"))
            .andExpect(jsonPath("$.name").value("World"))
            .andExpect(jsonPath("$.message").value("Test message"))
            .andExpect(jsonPath("$.timestamp").exists())
    }

    @Test
    fun `should return API info when GET request is made`() {
        mockMvc.perform(get("/api/hello"))
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.message").value("Hello API is running!"))
            .andExpect(jsonPath("$.endpoint").value("POST /api/hello"))
    }
}
